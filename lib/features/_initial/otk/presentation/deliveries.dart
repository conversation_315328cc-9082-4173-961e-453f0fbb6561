import 'package:auto_route/auto_route.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/otk/data/models/delivery.dart';
import 'package:sphere/features/_initial/otk/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class OtkDeliveriesScreen extends StatefulWidget {
  const OtkDeliveriesScreen({super.key});

  @override
  State<OtkDeliveriesScreen> createState() => _OtkDeliveriesScreenState();
}

class _OtkDeliveriesScreenState extends State<OtkDeliveriesScreen>
    with AutoRouteAwareStateMixin<OtkDeliveriesScreen> {
  bool _isLoading = false;
  List<OtkDeliveryModel> _deliveries = [];
  String? _error;

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    await _getData();
    _setAppBarConfig();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    await _getData();
    _setAppBarConfig();
  }

  Future<void> _getData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await OtkRepository.deliveriesSearch(SearchModel());
      if (response?.data != null) {
        setState(() {
          _deliveries = response!.data!.deliveries;
          _isLoading = false;
        });
        print('Loaded ${_deliveries.length} deliveries');
      } else {
        setState(() {
          _deliveries = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
      print('Error loading OTK deliveries: $e');
    }
  }

  void _setAppBarConfig() {
    final newConfig = CustomAppBarConfig(
      title: 'Отдел технического контроля',
      rightPart: Row(
        children: [
          TextButton(
            onPressed: () {
              context.router.push(const OtkDefectActsRoute());
            },
            child: const Text('Акты о браке'),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _getData,
            icon: SVG(Assets.icons.repeat, width: 20.0),
          ),
        ],
      ),
      isLoading: _isLoading,
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  void _showQualityControlDialog(OtkDeliveryModel delivery) {
    showBaseDialog(
      context,
      maxWidth: 1280,
      padding: EdgeInsets.all(0),
      builder: (context) => QualityControlDialog(
        delivery: delivery,
        onApprove: (items) async {
          print(delivery.id);
          try {
            await OtkRepository.deliveriesProcess(
              delivery.id!,
              items,
              [],
            );
            Navigator.of(context).pop();
            _getData(); // Обновляем данные
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Ошибка: $e')),
            );
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: Column(
        children: [
          // Заголовок
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Поставки на контроле качества',
                  style: Fonts.headlineSmall,
                ),
              ],
            ),
          ),

          // Ошибка
          if (_error != null)
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red),
              ),
              child: Row(
                children: [
                  Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(child: Text(_error!)),
                  IconButton(
                    onPressed: () => setState(() => _error = null),
                    icon: Icon(Icons.close, color: Colors.red),
                  ),
                ],
              ),
            ),

          // Таблица
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _deliveries.isEmpty
                    ? const Center(
                        child: Text('Нет поставок на контроле качества'),
                      )
                    : DataTable2(
                        minWidth: 800,
                        dividerThickness: 1.0,
                        showBottomBorder: true,
                        columnSpacing: 8,
                        columns: [
                          DataColumn2(
                            label:
                                Text('Дата приёмки', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Статус', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Поставщик', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Материалы', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Действия', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                        ],
                        rows: _deliveries
                            .map((delivery) => DataRow2(
                                  cells: [
                                    DataCell(Text(
                                      delivery.receivedAt != null
                                          ? DateFormat('dd.MM.yyyy HH:mm')
                                              .format(delivery.receivedAt!)
                                          : '',
                                      style: Fonts.bodySmall,
                                    )),
                                    DataCell(Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: delivery.status?.getColor(),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        delivery.status?.getName() ??
                                            'Неизвестно',
                                        style: Fonts.bodySmall
                                            .copyWith(color: Colors.white),
                                      ),
                                    )),
                                    DataCell(Text(
                                      delivery.delivery?.supplier?.name ?? '',
                                      style: Fonts.bodySmall,
                                    )),
                                    DataCell(Text(
                                      '${delivery.items?.length ?? 0}',
                                      style: Fonts.bodySmall,
                                    )),
                                    DataCell(Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        if (delivery.status ==
                                            OtkStatus.pendingQc)
                                          IconButton(
                                            tooltip: 'Контроль качества',
                                            onPressed: () =>
                                                _showQualityControlDialog(
                                              delivery,
                                            ),
                                            icon: const Icon(Icons.check_circle,
                                                size: 18),
                                          ),
                                        IconButton(
                                          tooltip: 'Просмотр деталей',
                                          onPressed: () {},
                                          icon: SVG(Assets.icons.visibility,
                                              width: 16),
                                        ),
                                      ],
                                    )),
                                  ],
                                ))
                            .toList(),
                      ),
          ),
        ],
      ),
    );
  }
}

class QualityControlDialog extends StatefulWidget {
  final OtkDeliveryModel delivery;
  final void Function(List<OtkProcessItem>) onApprove;

  const QualityControlDialog({
    super.key,
    required this.delivery,
    required this.onApprove,
  });

  @override
  State<QualityControlDialog> createState() => _QualityControlDialogState();
}

class _QualityControlDialogState extends State<QualityControlDialog> {
  final List<OtkProcessItem> _processData = [];
  final List<OtkItemModel> _itemsData = [];

  @override
  void initState() {
    super.initState();
    if (widget.delivery.items != null) {
      for (final item in widget.delivery.items!) {
        // Проверяем, что item является OtkItemModel
        _processData.add(
          OtkItemModel.toOtkProcessItem(item,
              acceptedQuantity:
                  // item.quantity
                  0),
        );
        _itemsData.add(item);
      }
    }
  }

  String _getSupplierName() {
    // Try to get supplier name from contract.supplier
    final supplier = widget.delivery.supplier;
    if (supplier != null &&
        supplier.name != null &&
        supplier.name!.isNotEmpty) {
      return supplier.name!;
    }
    // Fallback to supplierId if available
    final supplierId = widget.delivery.contract?.supplierId;
    if (supplierId != null && supplierId.isNotEmpty) {
      return supplierId;
    }
    return 'Неизвестный поставщик';
  }

  String _getProjectName() {
    final project = widget.delivery.project;
    if (project != null && project.name != null && project.name!.isNotEmpty) {
      return project.name!;
    }
    final projectId = widget.delivery.contract?.projectId;
    if (projectId != null && projectId.isNotEmpty) {
      return projectId;
    }
    return 'Неизвестный проект';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Заголовок
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Контроль качества поставки',
                  style: Fonts.headlineSmall,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
        ),

        // Содержимое
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Поставщик: ${_getSupplierName()}',
                  style: Fonts.bodyMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'Проект: ${_getProjectName()}',
                  style: Fonts.bodyMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'Материалы:',
                  style: Fonts.labelMedium,
                ),
                const SizedBox(height: 8),
                ...(_processData.asMap().entries.map((entry) =>
                    _buildItemRow(entry.value, _itemsData[entry.key]))),
              ],
            ),
          ),
        ),

        // Кнопки
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomElevatedButton(
                  onPressed: () {
                    widget.onApprove(
                      _processData.toList(),
                      // [],
                    );
                  },
                  type: CustomElevatedButtonTypes.accent,
                  text: 'Сохранить',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildItemRow(OtkProcessItem processData, OtkItemModel itemData) {
    TextEditingController acceptedController = TextEditingController(
      text: processData.acceptedQuantity?.toString() ?? '0',
    );
    // TextEditingController rejectedController = TextEditingController(
    //   text: processData.rejectedQuantity?.toString() ?? '0',
    // );

    void setAcceptedQuantity(String value) {
      final index =
          _processData.indexWhere((i) => i.productId == processData.productId);
      if (index != -1) {
        print(value);
        _processData[index] = processData.copyWith(
          acceptedQuantity: double.tryParse(value) ?? 0,
        );
      }
    }

    // void setRejectedQuantity(String value) {
    //   final index =
    //       _processData.indexWhere((i) => i.productId == processData.productId);
    //   if (index != -1) {
    //     print(value);
    //     _processData[index] = processData.copyWith(
    //       rejectedQuantity: double.tryParse(value) ?? 0,
    //     );
    //   }
    // }

    setAcceptedQuantity(acceptedController.value.text);
    // setRejectedQuantity(rejectedController.value.text);

    return CustomCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Text(
                itemData.product?.name ?? 'Неизвестный материал',
                style: Fonts.bodySmall,
              ),
            ),
            SizedBox(width: 8.0),
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  Text('Одобрить: ', style: Fonts.bodySmall),
                  SizedBox(width: 4.0),
                  Expanded(
                    child: TextField(
                      controller: acceptedController,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*')),
                      ],
                      onChanged: (value) {
                        setAcceptedQuantity(value);
                      },
                    ),
                  ),
                  SizedBox(width: 8.0),
                  // Text('Отклонить: ', style: Fonts.bodySmall),
                  // SizedBox(width: 4.0),
                  // Expanded(
                  //   child: TextField(
                  //     controller: rejectedController,
                  //     decoration: const InputDecoration(
                  //       border: OutlineInputBorder(),
                  //     ),
                  //     keyboardType: TextInputType.number,
                  //     inputFormatters: [
                  //       FilteringTextInputFormatter.allow(
                  //           RegExp(r'^\d*\.?\d*')),
                  //     ],
                  //     onChanged: (value) {
                  //       setRejectedQuantity(value);
                  //     },
                  //   ),
                  // ),
                  SizedBox(width: 4.0),
                  Text(
                    // '/ ${itemData.quantity?.toStringAsFixed(2) ?? '0'} ${itemData.baseUnit ?? ''}',
                    '/ 0',
                    style: Fonts.bodySmall.merge(
                      TextStyle(color: AppColors.lightDescription),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
