import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sphere/core/helpers/pick_file.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/deliveries/data/models/index.dart';
import 'package:sphere/features/deliveries/data/repositories/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class WarehouseAcceptanceDialog extends StatefulWidget {
  const WarehouseAcceptanceDialog({
    super.key,
    required this.delivery,
    required this.onSuccess,
  });

  final DeliveryModel delivery;
  final VoidCallback onSuccess;

  @override
  State<WarehouseAcceptanceDialog> createState() =>
      _WarehouseAcceptanceDialogState();
}

class _WarehouseAcceptanceDialogState extends State<WarehouseAcceptanceDialog> {
  final TextEditingController _commentController = TextEditingController();
  final List<TextEditingController> _quantityControllers = [];
  final List<TextEditingController> _itemCommentControllers = [];
  final List<File> _files = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Инициализируем контроллеры для каждого материала
    for (final item in widget.delivery.items ?? []) {
      _quantityControllers.add(
        TextEditingController(
          text: item.quantity?.toString() ?? '0',
        ),
      );
      _itemCommentControllers.add(TextEditingController());
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    for (final controller in _quantityControllers) {
      controller.dispose();
    }
    for (final controller in _itemCommentControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _addFile() async {
    final file = await pickFile();
    if (file != null) {
      setState(() {
        _files.add(file);
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Добавлен файл: ${file.path.split('/').last}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _removeFile(int index) {
    setState(() {
      _files.removeAt(index);
    });
  }

  Future<void> _submitAcceptance() async {
    // Валидация
    for (int i = 0; i < _quantityControllers.length; i++) {
      final quantity = double.tryParse(_quantityControllers[i].text);
      final maxQuantity = widget.delivery.items?[i].quantity ?? 0;

      if (quantity == null || quantity < 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Введите корректное количество для позиции ${i + 1}'),
            backgroundColor: AppColors.lightError,
          ),
        );
        return;
      }

      if (quantity > maxQuantity) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Количество не может превышать максимальное для позиции ${i + 1}'),
            backgroundColor: AppColors.lightError,
          ),
        );
        return;
      }
    }

    setState(() => _isLoading = true);

    try {
      // Создаем список элементов для приёмки
      final receiveItems = <ReceiveDeliveryItemModel>[];

      for (int i = 0; i < widget.delivery.items!.length; i++) {
        final item = widget.delivery.items![i];
        final quantity = double.tryParse(_quantityControllers[i].text) ?? 0;
        final location = _itemCommentControllers[i].text;

        receiveItems.add(ReceiveDeliveryItemModel(
          productId: item.productId,
          // materialId: item.productId,
          quantity: quantity,
          location: location.isNotEmpty ? location : null,
        ));
      }

      await DeliveriesRepository.receiveDelivery(
        deliveryId: widget.delivery.id!,
        items: receiveItems,
      );

      if (mounted) {
        Navigator.of(context).pop(true);
        widget.onSuccess();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Поставка успешно принята'),
            backgroundColor: AppColors.lightSuccess,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при приёмке: ${e.toString()}'),
            backgroundColor: AppColors.lightError,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final materials = widget.delivery.items ?? [];

    return Dialog(
      backgroundColor:
          isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Заголовок
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isDarkTheme
                        ? AppColors.darkStroke
                        : AppColors.lightStroke,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warehouse,
                    color: isDarkTheme
                        ? AppColors.darkPrimary
                        : AppColors.lightPrimary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Приёмка поставки',
                      style: Fonts.headlineSmall.copyWith(
                        color: isDarkTheme
                            ? AppColors.darkPrimary
                            : AppColors.lightPrimary,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Содержимое
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Информация о поставке
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isDarkTheme
                            ? AppColors.darkSurface
                            : AppColors.lightSurface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkTheme
                              ? AppColors.darkStroke
                              : AppColors.lightStroke,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Информация о поставке',
                            style: Fonts.titleMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          if (widget.delivery.supplier != null) ...[
                            Text(
                              'Поставщик: ${widget.delivery.supplier!.name ?? 'Не указан'}',
                              style: Fonts.bodyMedium,
                            ),
                            const SizedBox(height: 4),
                          ],
                          if (widget.delivery.expectedDate != null) ...[
                            Text(
                              'Ожидаемая дата: ${widget.delivery.expectedDate!.toString().split(' ')[0]}',
                              style: Fonts.bodyMedium,
                            ),
                            const SizedBox(height: 4),
                          ],
                          Text(
                            'Статус: ${widget.delivery.status?.getName() ?? 'Не указан'}',
                            style: Fonts.bodyMedium,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Материалы
                    Text(
                      'Материалы (${materials.length})',
                      style: Fonts.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),

                    ...materials.asMap().entries.map((entry) {
                      final index = entry.key;
                      final material = entry.value;
                      final maxQuantity = material.quantity ?? 0;
                      final unitType = material.unitType?.getName() ?? 'шт';

                      return Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: isDarkTheme
                              ? AppColors.darkSurface
                              : AppColors.lightSurface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isDarkTheme
                                ? AppColors.darkStroke
                                : AppColors.lightStroke,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    material.materialName ??
                                        'Материал ${index + 1}',
                                    style: Fonts.bodyLarge.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: isDarkTheme
                                        ? AppColors.darkPrimary
                                        : AppColors.lightPrimary,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    'Макс: $maxQuantity $unitType',
                                    style: Fonts.bodySmall.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),

                            // Количество
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _quantityControllers[index],
                                    decoration: InputDecoration(
                                      labelText: 'Количество к приёмке',
                                      border: const OutlineInputBorder(),
                                      suffixText: unitType,
                                      helperText:
                                          'Максимум: $maxQuantity $unitType',
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                            decimal: true),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\d*\.?\d*')),
                                    ],
                                    onChanged: (value) {
                                      final quantity =
                                          double.tryParse(value) ?? 0;
                                      if (quantity > maxQuantity) {
                                        _quantityControllers[index].text =
                                            maxQuantity.toString();
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 12),
                                GhostButton(
                                  onTap: _isLoading
                                      ? null
                                      : () {
                                          _quantityControllers[index].text =
                                              maxQuantity.toString();
                                        },
                                  child: const Text('Макс'),
                                ),
                              ],
                            ),

                            const SizedBox(height: 12),

                            // Комментарий к позиции
                            TextField(
                              controller: _itemCommentControllers[index],
                              decoration: const InputDecoration(
                                labelText:
                                    'Комментарий к позиции (необязательно)',
                                border: OutlineInputBorder(),
                                hintText: 'Укажите особенности приёмки...',
                              ),
                              maxLines: 2,
                            ),
                          ],
                        ),
                      );
                    }),

                    const SizedBox(height: 20),

                    // Общий комментарий
                    TextField(
                      controller: _commentController,
                      decoration: const InputDecoration(
                        labelText:
                            'Общий комментарий к приёмке (необязательно)',
                        border: OutlineInputBorder(),
                        hintText: 'Укажите общие замечания по поставке...',
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 20),

                    // Файлы
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Документы',
                            style: Fonts.titleMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        GhostButton(
                          onTap: _isLoading ? null : _addFile,
                          child: const Text('Добавить файл'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    if (_files.isNotEmpty) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isDarkTheme
                              ? AppColors.darkSurface
                              : AppColors.lightSurface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isDarkTheme
                                ? AppColors.darkStroke
                                : AppColors.lightStroke,
                          ),
                        ),
                        child: Column(
                          children: _files.asMap().entries.map((entry) {
                            final index = entry.key;
                            final file = entry.value;

                            return ListTile(
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(
                                Icons.insert_drive_file,
                                color: isDarkTheme
                                    ? AppColors.darkPrimary
                                    : AppColors.lightPrimary,
                              ),
                              title: Text(
                                file.path.split('/').last,
                                style: Fonts.bodyMedium,
                              ),
                              subtitle: Text(
                                '${(file.lengthSync() / 1024).toStringAsFixed(1)} KB',
                                style: Fonts.bodySmall,
                              ),
                              trailing: IconButton(
                                onPressed: _isLoading
                                    ? null
                                    : () => _removeFile(index),
                                icon: const Icon(Icons.close),
                                color: AppColors.lightError,
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                  ],
                ),
              ),
            ),

            // Кнопки
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: isDarkTheme
                        ? AppColors.darkStroke
                        : AppColors.lightStroke,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: GhostButton(
                      onTap:
                          _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('Отмена'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submitAcceptance,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isDarkTheme
                            ? AppColors.darkPrimary
                            : AppColors.lightPrimary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('Принять поставку'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
