import 'package:auto_route/auto_route.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/deliveries/domain/column.dart';
import 'package:sphere/features/deliveries/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/deliveries/presentation/column_visibility_dialog.dart';
import 'package:sphere/features/deliveries/presentation/filters/filter_panel.dart';
import 'package:sphere/features/deliveries/presentation/warehouse_acceptance_dialog.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class DeliveriesScreen extends StatefulWidget {
  const DeliveriesScreen({
    super.key,
    @PathParam('id') this.id,
    this.projectName,
  });

  final String? id;
  final String? projectName;

  @override
  State<DeliveriesScreen> createState() => _DeliveriesScreenState();
}

class _DeliveriesScreenState extends State<DeliveriesScreen> {
  int _sortColumnIndex = 0;
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    // Initialize filters and load data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BlocDeliveries>().add(ApplyFilters(SearchModel(
            filters: SearchFiltersModel(
              projectId: widget.id,
            ),
          )));
    });
  }

  void _refreshData() {
    if (mounted) {
      context.read<BlocDeliveries>().add(LoadDataWithFilters());
    }
  }

  dynamic _getSortValue(DeliveryModel delivery, int columnIndex) {
    switch (columnIndex) {
      case 0:
        return delivery.createdAt;
      case 1:
        return delivery.status?.getName() ?? '';
      case 2:
        return delivery.supplier?.name ?? '';
      case 3:
        return delivery.expectedDate;
      case 4:
        return delivery.deliveryDate;
      case 5:
        return delivery.items?.length ?? 0;
      case 6:
        return delivery.items
                ?.fold<double>(0, (sum, item) => sum + (item.quantity ?? 0)) ??
            0;
      case 7:
        return delivery.items?.fold<double>(
                0,
                (sum, item) =>
                    sum + ((item.price ?? 0) * (item.quantity ?? 0))) ??
            0;
      case 8:
        return delivery.comment ?? '';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BlocDeliveries, BlocDeliveriesState>(
      builder: (context, state) {
        final visibleColumnIndexes = DeliveryTableData.columns
            .asMap()
            .entries
            .where((e) => state.columnVisibility[e.key.toString()] ?? true)
            .map((e) => e.key)
            .toList();

        final sortedDeliveries = List<DeliveryModel>.from(state.deliveries)
          ..sort((a, b) {
            final aValue = _getSortValue(a, _sortColumnIndex);
            final bValue = _getSortValue(b, _sortColumnIndex);
            if (aValue is Comparable && bValue is Comparable) {
              return _sortAscending
                  ? aValue.compareTo(bValue)
                  : bValue.compareTo(aValue);
            }
            return 0;
          });

        return Wrapper(
          appBar: CustomAppBar(
            title: widget.projectName,
            description: 'Поставки',
            rightPart: Row(
              children: [
                if (state.selectedDeliveries.isNotEmpty)
                  TextButton(
                    child: Row(
                      children: [
                        SVG(Assets.icons.close, width: 18.0),
                        SizedBox(width: 8.0),
                        Text(
                          'Снять выделение (${state.selectedDeliveries.length})',
                          style: Fonts.labelSmall.merge(
                            TextStyle(fontSize: 14.0),
                          ),
                        ),
                      ],
                    ),
                    onPressed: () {
                      context
                          .read<BlocDeliveries>()
                          .add(ClearDeliverySelections());
                    },
                  ),
                IconButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => BlocProvider.value(
                        value: context.read<BlocDeliveries>(),
                        child: const DeliveryColumnVisibilityDialog(),
                      ),
                    );
                  },
                  icon: SVG(Assets.icons.settings, width: 20.0),
                ),
                IconButton(
                  onPressed: () {
                    context.read<BlocDeliveries>().add(ToggleFiltersPanel());
                  },
                  icon: SVG(Assets.icons.functions, width: 20.0),
                ),
                IconButton(
                  onPressed: _refreshData,
                  icon: SVG(Assets.icons.repeat, width: 20.0),
                ),
              ],
            ),
            isLoading: state.isLoading,
          ),
          body: Column(
            children: [
              // Filters panel
              if (state.isFiltersOpen)
                SizedBox(
                  width: double.infinity,
                  child: DeliveryFilterPanel(projectId: widget.id),
                ),

              // Table
              Expanded(
                child: state.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : state.deliveries.isEmpty
                        ? const Center(child: Text('Нет данных'))
                        : DataTable2(
                            minWidth: visibleColumnIndexes.fold(
                                100.0,
                                (sum, i) =>
                                    sum! +
                                    (DeliveryTableData.columns[i].width ??
                                        0.0)),
                            dividerThickness: 1.0,
                            sortArrowBuilder: (ascending, sorted) => sorted
                                ? SVG(ascending
                                    ? Assets.icons.keyboardArrowUp
                                    : Assets.icons.keyboardArrowDown)
                                : const SizedBox(),
                            sortColumnIndex: _sortColumnIndex,
                            sortAscending: _sortAscending,
                            showCheckboxColumn: true,
                            showBottomBorder: true,
                            columnSpacing: 8,
                            columns: visibleColumnIndexes
                                .map((i) => DataColumn2(
                                      onSort: (index, ascending) {
                                        setState(() {
                                          final originalIndex =
                                              visibleColumnIndexes[index];
                                          _sortColumnIndex = originalIndex;
                                          _sortAscending = ascending;
                                        });
                                      },
                                      label: Text(
                                        DeliveryTableData.columns[i].name ?? '',
                                        style: Fonts.labelMedium,
                                      ),
                                      size: ColumnSize.L,
                                    ))
                                .toList(),
                            rows: sortedDeliveries
                                .map((delivery) => DataRow2(
                                      selected: state.selectedDeliveries
                                          .containsKey(delivery.id),
                                      onSelectChanged: (selected) {
                                        context.read<BlocDeliveries>().add(
                                            ToggleDeliverySelection(delivery));
                                      },
                                      cells: visibleColumnIndexes
                                          .map((i) =>
                                              DataCell(_buildCell(delivery, i)))
                                          .toList(),
                                    ))
                                .toList(),
                          ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCell(DeliveryModel delivery, int columnIndex) {
    switch (columnIndex) {
      case 0: // Дата создания
        return Text(
          delivery.createdAt != null
              ? DateFormat('dd.MM.yyyy').format(delivery.createdAt!)
              : '',
          style: Fonts.bodySmall,
        );
      case 1: // Статус
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(delivery.status),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            delivery.status?.getName() ?? 'Неизвестно',
            style: Fonts.bodySmall.copyWith(color: Colors.white),
          ),
        );
      case 2: // Поставщик
        return Text(
          delivery.supplier?.name ?? '',
          style: Fonts.bodySmall,
        );
      case 3: // Ожидаемая дата
        return Text(
          delivery.expectedDate != null
              ? DateFormat('dd.MM.yyyy').format(delivery.expectedDate!)
              : '',
          style: Fonts.bodySmall,
        );
      case 4: // Фактическая дата
        return Text(
          delivery.deliveryDate != null
              ? DateFormat('dd.MM.yyyy').format(delivery.deliveryDate!)
              : '',
          style: Fonts.bodySmall,
        );
      case 5: // Количество материалов
        return Text(
          '${delivery.items?.length ?? 0}',
          style: Fonts.bodySmall,
        );
      case 6: // Общее количество
        final totalQuantity = delivery.items
                ?.fold<double>(0, (sum, item) => sum + (item.quantity ?? 0)) ??
            0;
        return Text(
          totalQuantity.toStringAsFixed(2),
          style: Fonts.bodySmall,
        );
      case 7: // Общая стоимость
        final totalCost = delivery.items?.fold<double>(
                0,
                (sum, item) =>
                    sum + ((item.price ?? 0) * (item.quantity ?? 0))) ??
            0;
        return Text(
          '${totalCost.toStringAsFixed(2)} ₽',
          style: Fonts.bodySmall,
        );
      case 8: // Комментарий
        return Text(
          delivery.comment ?? '',
          style: Fonts.bodySmall,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        );
      case 9: // Действия
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () {
                // TODO: Implement view delivery details
              },
              icon: SVG(Assets.icons.visibility, width: 16),
            ),
            IconButton(
              onPressed: () {
                // TODO: Implement edit delivery
              },
              icon: SVG(Assets.icons.edit, width: 16),
            ),
            if (delivery.status == DeliveryStatus.pending ||
                delivery.status == DeliveryStatus.qcRejected)
              IconButton(
                tooltip: 'Принять поставку',
                onPressed: () async {
                  final result = await showDialog(
                    context: context,
                    builder: (context) => WarehouseAcceptanceDialog(
                      delivery: delivery,
                      onSuccess: _refreshData,
                    ),
                  );
                  if (result == true) _refreshData();
                },
                icon: const Icon(Icons.warehouse, size: 18),
              ),
          ],
        );
      default:
        return const SizedBox();
    }
  }

  Color _getStatusColor(DeliveryStatus? status) {
    switch (status) {
      case DeliveryStatus.pending:
        return Colors.orange;
      case DeliveryStatus.qcPending:
        return Colors.blue;
      case DeliveryStatus.delivered:
        return Colors.green;
      case DeliveryStatus.cancelled:
        return Colors.red;
      case DeliveryStatus.qcRejected:
        return Colors.red.shade700;
      case DeliveryStatus.qcPartRejected:
        return Colors.orange.shade700;
      default:
        return Colors.grey;
    }
  }
}
